from flask import Flask, request, jsonify
import os
import google.generativeai as genai
import logging
from dotenv import load_dotenv
from database import db_manager
from user_service import user_service

# Load environment variables from .env file
load_dotenv(os.path.join(os.path.dirname(__file__), '.env'))

app = Flask(__name__)
logging.basicConfig(level=logging.INFO)

# Load environment variables
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")

# Configure APIs
genai.configure(api_key=GEMINI_API_KEY)



def process_text_query(query):
    try:
        model = genai.GenerativeModel("gemini-1.5-flash-latest")
        prompt = f"""You are a JEE and NEET Tutor AI assistant.
- Fetch relevant information from provided documents based on the user's query and explain it with clear, complete sentences.
- Combine document content with your own knowledge for comprehensive, exam-oriented answers tailored to JEE and NEET.
- Rely on NCERT-based explanations for NEET topics and emphasize conceptual depth with examples for JEE topics.
- Responses should be between 100 and 150 tokens.
- If the query is unrelated to JEE or NEET, respond with: "I'm here to assist with JEE/NEET preparation. Please share a query related to these exams, and I'll help you!"
- Provide examples or detailed explanations when needed.
- Maintain clarity, relevance, and a supportive tone.

User question: {query}"""
        response = model.generate_content(prompt)
        return response.text
    except Exception as e:
        logging.error(f"Error in process_text_query: {e}")
        return "I'm experiencing some technical difficulties. Please try again in a moment."



# API endpoint for processing text queries from n8n
@app.route("/api/process-text", methods=["POST"])
def api_process_text():
    try:
        data = request.get_json()
        query = data.get("query", "")

        if not query:
            return jsonify({"error": "Query is required"}), 400

        logging.info(f"Processing text query via API: {query}")

        # Parse the query to extract user message and phone number
        user_message, phone_number = user_service.parse_query_data(query)

        if not user_message:
            return jsonify({"error": "User message is required"}), 400

        # Get AI response for the user message
        ai_response = process_text_query(user_message)

        # Look up user by phone number if provided
        user_data = None
        if phone_number:
            user_data = user_service.get_user_by_phone(phone_number)

        # Personalize the response if user found
        first_name = user_data.get('first_name') if user_data else None
        personalized_response = user_service.format_personalized_response(ai_response, first_name)

        return jsonify({
            "success": True,
            "response": personalized_response,
            "user_found": user_data is not None,
            "user_name": first_name
        }), 200

    except Exception as e:
        logging.error(f"Error in API text processing: {e}")
        return jsonify({
            "success": False,
            "error": "Failed to process text query",
            "response": "I'm experiencing some technical difficulties. Please try again in a moment."
        }), 500





@app.route("/health", methods=["GET"])
def health_check():
    # Ensure database connectivity and test
    db_healthy = db_manager.ensure_database_connection() and db_manager.test_connection()

    return jsonify({
        "status": "healthy" if db_healthy else "degraded",
        "service": "JEE/NEET Text Processing Bot",
        "gemini_configured": bool(GEMINI_API_KEY),
        "database_connected": db_healthy
    }), 200 if db_healthy else 503

@app.route("/test", methods=["GET"])
def test_endpoint():
    return jsonify({
        "message": "JEE/NEET Text Processing Bot is running!",
        "available_endpoints": ["/api/process-text", "/health", "/test", "/db-test"]
    }), 200

@app.route("/db-test", methods=["GET"])
def database_test():
    """Test database connectivity and user lookup functionality"""
    try:
        # Ensure and test database connection
        db_ensured = db_manager.ensure_database_connection()
        db_connected = db_manager.test_connection()

        if not db_ensured or not db_connected:
            return jsonify({
                "success": False,
                "message": "Database connection failed",
                "connection_ensured": db_ensured,
                "connection_tested": db_connected
            }), 503

        # Test user lookup with a sample query
        test_response = {
            "success": True,
            "message": "Database connection successful",
            "database_connected": True,
            "connection_ensured": db_ensured,
            "endpoints_available": [
                "POST /api/process-text - Main API for processing user queries with personalization"
            ]
        }

        return jsonify(test_response), 200

    except Exception as e:
        logging.error(f"Database test error: {e}")
        return jsonify({
            "success": False,
            "message": f"Database test failed: {str(e)}"
        }), 500

if __name__ == "__main__":
    # Ensure database connection on startup
    try:
        if db_manager.ensure_database_connection():
            logging.info("Database connection ensured and established successfully")
        else:
            logging.warning("Database connection failed - app will run with limited functionality")
    except Exception as e:
        logging.error(f"Database initialization error: {e}")
        logging.info("App will continue to run and attempt reconnection on demand")

    port = int(os.getenv("PORT", 8035))
    app.run(host="0.0.0.0", port=port, debug=False)
