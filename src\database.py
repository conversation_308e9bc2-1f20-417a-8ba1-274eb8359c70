import psycopg2
from psycopg2 import pool
import os
import logging
from contextlib import contextmanager
from dotenv import load_dotenv

# Configure logging
logger = logging.getLogger(__name__)

class DatabaseManager:
    """Database connection manager with connection pooling"""

    def __init__(self):
        self.connection_pool = None
        self._initialized = False
    
    def _initialize_pool(self):
        """Initialize the connection pool"""
        if self._initialized:
            return

        try:
            # Load environment variables if not already loaded
            load_dotenv(os.path.join(os.path.dirname(__file__), '.env'))

            # Get database credentials from environment variables
            db_config = {
                'host': os.getenv('DB_HOST'),
                'port': os.getenv('DB_PORT', 5432),
                'database': os.getenv('DB_NAME'),
                'user': os.getenv('DB_USER'),
                'password': os.getenv('DB_PASSWORD')
            }

            # Validate required environment variables
            required_vars = ['DB_HOST', 'DB_NAME', 'DB_USER', 'DB_PASSWORD']
            missing_vars = [var for var in required_vars if not os.getenv(var)]

            if missing_vars:
                raise ValueError(f"Missing required environment variables: {missing_vars}")

            # Create connection pool
            self.connection_pool = psycopg2.pool.SimpleConnectionPool(
                minconn=1,
                maxconn=10,
                **db_config
            )

            self._initialized = True
            logger.info("Database connection pool initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize database connection pool: {e}")
            raise
    
    @contextmanager
    def get_connection(self):
        """Context manager for database connections with auto-reconnection"""
        # Initialize if not already done
        if not self._initialized:
            self._initialize_pool()

        connection = None
        try:
            if not self.connection_pool:
                raise Exception("Database connection pool not initialized")

            connection = self.connection_pool.getconn()
            yield connection

        except Exception as e:
            if connection:
                try:
                    connection.rollback()
                except Exception as rollback_error:
                    logger.warning(f"Error during rollback: {rollback_error}")

            # Check if it's a connection-related error and try to reconnect
            if "connection" in str(e).lower() or "closed" in str(e).lower():
                logger.warning(f"Connection error detected: {e}. Attempting reconnection...")
                try:
                    self._reinitialize_pool()
                    # Retry getting connection after reconnection
                    if self.connection_pool:
                        connection = self.connection_pool.getconn()
                        yield connection
                        return
                except Exception as reconnect_error:
                    logger.error(f"Reconnection failed: {reconnect_error}")

            logger.error(f"Database connection error: {e}")
            raise

        finally:
            if connection and self.connection_pool:
                try:
                    self.connection_pool.putconn(connection)
                except Exception as putconn_error:
                    logger.warning(f"Error returning connection to pool: {putconn_error}")
    
    def ensure_database_connection(self):
        """Ensure database connection is active and reconnect if necessary"""
        try:
            # If not initialized, initialize the pool
            if not self._initialized:
                logger.info("Database not initialized, initializing connection pool...")
                self._initialize_pool()
                return True

            # Test current connection pool directly without using get_connection
            if self.connection_pool:
                try:
                    # Get a connection directly from pool for testing
                    test_conn = self.connection_pool.getconn()
                    try:
                        with test_conn.cursor() as cursor:
                            cursor.execute("SELECT 1")
                            result = cursor.fetchone()
                            logger.debug("Database connection is healthy")
                            return True
                    finally:
                        # Return connection to pool
                        self.connection_pool.putconn(test_conn)

                except Exception as conn_error:
                    logger.warning(f"Database connection test failed: {conn_error}")
                    # Connection failed, try to reinitialize
                    logger.info("Attempting to reinitialize database connection...")
                    self._reinitialize_pool()
                    return True
            else:
                # No connection pool, initialize it
                logger.info("No connection pool found, initializing...")
                self._initialize_pool()
                return True

        except Exception as e:
            logger.error(f"Failed to ensure database connection: {e}")
            return False

    def _reinitialize_pool(self):
        """Reinitialize the connection pool"""
        try:
            # Close existing connections if any
            if self.connection_pool:
                try:
                    self.connection_pool.closeall()
                except Exception as close_error:
                    logger.warning(f"Error closing existing connections: {close_error}")

            # Reset state
            self.connection_pool = None
            self._initialized = False

            # Reinitialize
            self._initialize_pool()
            logger.info("Database connection pool reinitialized successfully")

        except Exception as e:
            logger.error(f"Failed to reinitialize database connection pool: {e}")
            raise

    def test_connection(self):
        """Test database connectivity"""
        try:
            if not self._initialized:
                self._initialize_pool()

            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    result = cursor.fetchone()
                    logger.info("Database connection test successful")
                    return True
        except Exception as e:
            logger.error(f"Database connection test failed: {e}")
            return False
    
    def close_all_connections(self):
        """Close all connections in the pool"""
        if self.connection_pool:
            self.connection_pool.closeall()
            logger.info("All database connections closed")

# Global database manager instance
db_manager = DatabaseManager()
