import logging
from typing import Optional, Dict, Any
from database import db_manager

# Configure logging
logger = logging.getLogger(__name__)

class UserService:
    """Service class for user-related database operations"""
    
    @staticmethod
    def get_user_by_phone(phone_number: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve user information by phone number from students table

        Args:
            phone_number (str): The phone number to search for

        Returns:
            Optional[Dict[str, Any]]: User information if found, None otherwise
        """
        try:
            # Clean phone number (remove any whitespace)
            phone_number = phone_number.strip()

            if not phone_number:
                logger.warning("Empty phone number provided")
                return None

            # Ensure database connection before proceeding
            if not db_manager.ensure_database_connection():
                logger.error("Unable to establish database connection for user lookup")
                return None

            with db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    # Query to get user information by phone number
                    query = """
                        SELECT first_name, phone
                        FROM students
                        WHERE phone = %s
                        LIMIT 1
                    """

                    cursor.execute(query, (phone_number,))
                    result = cursor.fetchone()

                    if result:
                        user_data = {
                            'first_name': result[0],
                            'phone': result[1]
                        }
                        logger.info(f"User found for phone number: {phone_number}")
                        return user_data
                    else:
                        logger.info(f"No user found for phone number: {phone_number}")
                        return None

        except Exception as e:
            logger.error(f"Error retrieving user by phone {phone_number}: {e}")
            return None
    
    @staticmethod
    def parse_query_data(query_string: str) -> tuple[str, str]:
        """
        Parse the query string from n8n to extract user message and phone number
        
        Expected format from n8n:
        "user_message\nphone_number"
        
        Args:
            query_string (str): The query string from n8n
            
        Returns:
            tuple[str, str]: (user_message, phone_number)
        """
        try:
            if not query_string:
                return "", ""
            
            # Split by newline to separate message and phone number
            parts = query_string.strip().split('\n')
            
            if len(parts) >= 2:
                user_message = parts[0].strip()
                phone_number = parts[1].strip()
                
                logger.info(f"Parsed query - Message: '{user_message}', Phone: '{phone_number}'")
                return user_message, phone_number
            else:
                # If only one part, treat it as the message
                user_message = parts[0].strip() if parts else ""
                logger.warning(f"Could not parse phone number from query: {query_string}")
                return user_message, ""
                
        except Exception as e:
            logger.error(f"Error parsing query data: {e}")
            return query_string, ""
    
    @staticmethod
    def format_personalized_response(response: str, first_name: Optional[str] = None) -> str:
        """
        Format the AI response with personalization if user name is available
        
        Args:
            response (str): The original AI response
            first_name (Optional[str]): User's first name if available
            
        Returns:
            str: Personalized response
        """
        try:
            if first_name and first_name.strip():
                # Add personalized greeting
                personalized_response = f"Hi {first_name.strip()}, {response}"
                logger.info(f"Response personalized for user: {first_name}")
                return personalized_response
            else:
                # Return original response without personalization
                logger.info("Response sent without personalization")
                return response
                
        except Exception as e:
            logger.error(f"Error formatting personalized response: {e}")
            return response

# Create a global instance for easy access
user_service = UserService()
